# Wallet Configuration
# Your Solana wallet private key in base58 format
WALLET_PRIVATE_KEY=your_base58_private_key_here

# Additional wallet private keys for scaling (optional)
WALLET_PRIVATE_KEY_2=
WALLET_PRIVATE_KEY_3=
WALLET_PRIVATE_KEY_4=
WALLET_PRIVATE_KEY_5=

# API Keys
# Helius API key for rugcheck and enhanced RPC
HELIUS_API_KEY=your_helius_api_key_here

# Optional: Additional RPC endpoints
QUICKNODE_RPC_URL=
ALCHEMY_RPC_URL=
TRITON_RPC_URL=

# Optional: Telegram notifications
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# Optional: Discord webhook for notifications
DISCORD_WEBHOOK_URL=

# Development Settings
DEBUG=false
LOG_LEVEL=INFO

# Database Configuration (optional - defaults to SQLite)
DATABASE_URL=sqlite:///trades.db

# Security Settings
ENCRYPTION_KEY=your_32_byte_encryption_key_here
