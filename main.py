#!/usr/bin/env python3
"""
AutoSniper - Main orchestration loop
Entry point for the Pump.fun auto-trading bot
"""

import asyncio
import json
import logging
import os
import signal
import sys
from datetime import datetime
from typing import Dict, List, Optional

from dotenv import load_dotenv

# Import our modules
from scanner import TokenScanner
from filter import TokenFilter
from helius_check import <PERSON><PERSON><PERSON><PERSON><PERSON>
from executor import TradeExecutor
from monitor import PriceMonitor
from wallet import WalletManager
from logger import TradeLogger

# Load environment variables
load_dotenv()

class AutoSniper:
    """Main orchestration class for the auto-sniping bot"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config = self.load_config(config_path)
        self.running = False
        self.active_trades = {}
        self.trade_count = 0
        
        # Initialize components
        self.scanner = TokenScanner(self.config)
        self.filter = TokenFilter(self.config)
        self.rugchecker = <PERSON>ug<PERSON><PERSON><PERSON>(self.config)
        self.executor = TradeExecutor(self.config)
        self.monitor = PriceMonitor(self.config)
        self.wallet_manager = WalletManager(self.config)
        self.trade_logger = TradeLogger(self.config)
        
        self.setup_logging()
        self.setup_signal_handlers()
    
    def load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            logging.error(f"Config file {config_path} not found")
            sys.exit(1)
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in config file: {e}")
            sys.exit(1)
    
    def setup_logging(self):
        """Configure logging based on config settings"""
        log_level = getattr(logging, self.config['logging']['log_level'].upper())
        
        # Configure root logger
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout) if self.config['logging']['enable_console_output'] else logging.NullHandler(),
                logging.FileHandler(self.config['logging']['log_file']) if self.config['logging']['log_to_file'] else logging.NullHandler()
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("AutoSniper initialized")
    
    def setup_signal_handlers(self):
        """Setup graceful shutdown handlers"""
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
    
    async def start(self):
        """Start the main trading loop"""
        self.logger.info("Starting AutoSniper bot...")
        self.running = True
        
        # Validate configuration and environment
        if not await self.validate_setup():
            return
        
        # Start main loop
        await self.main_loop()
    
    async def validate_setup(self) -> bool:
        """Validate configuration and environment setup"""
        # Check required environment variables
        required_env_vars = ['WALLET_PRIVATE_KEY', 'HELIUS_API_KEY']
        missing_vars = [var for var in required_env_vars if not os.getenv(var)]
        
        if missing_vars:
            self.logger.error(f"Missing required environment variables: {missing_vars}")
            return False
        
        # Update wallet balances and validate
        await self.wallet_manager.update_wallet_balances()
        if not self.wallet_manager.validate_wallet_setup():
            return False
        
        self.logger.info("Setup validation passed")
        return True
    
    async def main_loop(self):
        """Main trading loop - coordinates all components"""
        self.logger.info("Starting main trading loop...")
        
        try:
            while self.running:
                # 1. Scan for new tokens
                async with self.scanner as scanner:
                    new_tokens = await scanner.get_new_tokens()

                if new_tokens:
                    # 2. Apply filters
                    filtered_tokens = self.filter.apply_filters(new_tokens)

                    # 3. Check if we can take new positions
                    max_trades = self.config['trading']['max_concurrent_trades']
                    if len(self.active_trades) < max_trades and filtered_tokens:
                        # Process top filtered tokens
                        await self.process_trading_opportunities(filtered_tokens)

                # 4. Monitor existing trades
                await self.monitor_active_trades()

                # 5. Wait before next iteration
                await asyncio.sleep(self.config['monitoring']['scanner_interval'])
                
        except Exception as e:
            self.logger.error(f"Error in main loop: {e}")
        finally:
            await self.cleanup()
    
    async def process_trading_opportunities(self, filtered_tokens: List[Dict]):
        """Process filtered tokens and execute trades"""
        max_new_trades = self.config['trading']['max_concurrent_trades'] - len(self.active_trades)

        for i, token in enumerate(filtered_tokens[:max_new_trades]):
            try:
                # Perform rugcheck
                async with self.rugchecker as checker:
                    safety_result = await checker.check_token_safety(token['mint_address'])

                if not safety_result['safe']:
                    self.logger.info(f"Token {token['mint_address']} failed safety check: {safety_result['reason']}")
                    continue

                # Allocate wallet for trade
                wallet = self.wallet_manager.allocate_wallet_for_trade()
                if not wallet:
                    self.logger.warning("No wallets available for trading")
                    break

                # Execute buy order
                trade_data = {
                    **token,
                    'wallet_address': wallet['address'],
                    'entry_timestamp': datetime.now().isoformat(),
                    'safety_checks': safety_result
                }

                buy_result = await self.executor.execute_buy(trade_data)

                if buy_result['success']:
                    # Log trade entry
                    trade_id = self.trade_logger.log_trade_entry({
                        **trade_data,
                        **buy_result
                    })

                    # Start monitoring
                    self.active_trades[trade_id] = {
                        **trade_data,
                        **buy_result,
                        'trade_id': trade_id
                    }

                    async with self.monitor as monitor:
                        await monitor.start_monitoring(
                            self.active_trades[trade_id],
                            self.handle_trade_exit
                        )

                    self.logger.info(f"Started new trade: {trade_id}")
                else:
                    self.logger.error(f"Failed to execute buy: {buy_result['error']}")
                    self.wallet_manager.release_wallet_from_trade(wallet['address'])

            except Exception as e:
                self.logger.error(f"Error processing token {token.get('mint_address', 'unknown')}: {e}")

    async def handle_trade_exit(self, trade_id: str, exit_reason: str, current_data: Dict):
        """Handle trade exit callback from monitor"""
        if trade_id not in self.active_trades:
            return

        trade_data = self.active_trades[trade_id]

        try:
            # Execute sell order
            sell_result = await self.executor.execute_sell(trade_data)

            if sell_result['success']:
                # Log trade exit
                exit_data = {
                    **sell_result,
                    'exit_reason': exit_reason,
                    'exit_timestamp': datetime.now().isoformat()
                }

                self.trade_logger.log_trade_exit(trade_id, exit_data)

                # Release wallet
                self.wallet_manager.release_wallet_from_trade(trade_data['wallet_address'])

                # Remove from active trades
                del self.active_trades[trade_id]

                self.logger.info(f"Completed trade {trade_id}: {exit_reason}")
            else:
                self.logger.error(f"Failed to execute sell for {trade_id}: {sell_result['error']}")

        except Exception as e:
            self.logger.error(f"Error handling trade exit for {trade_id}: {e}")

    async def monitor_active_trades(self):
        """Monitor and manage active trades"""
        if not self.active_trades:
            return

        # Log current status
        if len(self.active_trades) > 0:
            stats = self.monitor.get_monitoring_stats() if hasattr(self.monitor, 'get_monitoring_stats') else {}
            self.logger.debug(f"Active trades: {len(self.active_trades)}, Stats: {stats}")
    
    async def cleanup(self):
        """Cleanup resources and close positions"""
        self.logger.info("Cleaning up...")

        # Close any remaining positions
        for trade_id in list(self.active_trades.keys()):
            try:
                await self.handle_trade_exit(trade_id, "Bot shutdown", {})
            except Exception as e:
                self.logger.error(f"Error closing position {trade_id}: {e}")

        # Close component connections
        try:
            await self.executor.close()
            await self.monitor.close()
            await self.wallet_manager.close()
        except Exception as e:
            self.logger.error(f"Error closing connections: {e}")

        self.logger.info("Cleanup completed")

def main():
    """Entry point"""
    print("🚀 AutoSniper - Pump.fun Auto-Trading Bot")
    print("=" * 50)
    
    # Check if running in development mode
    if len(sys.argv) > 1 and sys.argv[1] == "--dev":
        print("⚠️  Running in development mode")
    
    # Create and start the bot
    bot = AutoSniper()
    
    try:
        asyncio.run(bot.start())
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
