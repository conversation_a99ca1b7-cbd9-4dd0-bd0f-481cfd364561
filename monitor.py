"""
Price Monitor - Real-time price tracking and exit strategy execution
Monitors token prices and triggers exits based on TP/SL/curve migration thresholds
"""

import asyncio
import aiohttp
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable

class PriceMonitor:
    """Monitors token prices and manages exit strategies"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.session = None
        self.active_monitors = {}
        self.dexscreener_base = config['apis']['dexscreener_base_url']
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config['monitoring']['timeout_seconds'])
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def start_monitoring(self, trade_data: Dict, exit_callback: Callable):
        """Start monitoring a trade position"""
        trade_id = trade_data['trade_id']
        mint_address = trade_data['mint_address']
        
        self.logger.info(f"Starting price monitoring for trade {trade_id} ({mint_address})")
        
        monitor_data = {
            'trade_data': trade_data,
            'exit_callback': exit_callback,
            'start_time': datetime.now(),
            'entry_price': trade_data['entry_price'],
            'highest_price': trade_data['entry_price'],
            'monitoring': True
        }
        
        self.active_monitors[trade_id] = monitor_data
        
        # Start monitoring task
        asyncio.create_task(self._monitor_trade(trade_id))
    
    async def stop_monitoring(self, trade_id: str):
        """Stop monitoring a trade"""
        if trade_id in self.active_monitors:
            self.active_monitors[trade_id]['monitoring'] = False
            del self.active_monitors[trade_id]
            self.logger.info(f"Stopped monitoring trade {trade_id}")
    
    async def _monitor_trade(self, trade_id: str):
        """Main monitoring loop for a single trade"""
        monitor_data = self.active_monitors.get(trade_id)
        if not monitor_data:
            return
        
        trade_data = monitor_data['trade_data']
        mint_address = trade_data['mint_address']
        
        try:
            while monitor_data.get('monitoring', False):
                # Get current price and bonding curve data
                current_data = await self.get_current_price_data(mint_address)
                
                if current_data:
                    # Update monitoring data
                    current_price = current_data['price']
                    monitor_data['current_price'] = current_price
                    monitor_data['bonding_curve_percent'] = current_data.get('bonding_curve_percent', 0)
                    
                    # Update highest price for trailing stop
                    if current_price > monitor_data['highest_price']:
                        monitor_data['highest_price'] = current_price
                    
                    # Check exit conditions
                    exit_reason = self.check_exit_conditions(monitor_data)
                    
                    if exit_reason:
                        self.logger.info(f"Exit triggered for {trade_id}: {exit_reason}")
                        
                        # Execute exit callback
                        await monitor_data['exit_callback'](trade_id, exit_reason, current_data)
                        
                        # Stop monitoring
                        monitor_data['monitoring'] = False
                        break
                
                # Wait before next price check
                await asyncio.sleep(self.config['monitoring']['price_check_interval'])
                
        except Exception as e:
            self.logger.error(f"Error monitoring trade {trade_id}: {e}")
        finally:
            # Cleanup
            if trade_id in self.active_monitors:
                del self.active_monitors[trade_id]
    
    async def get_current_price_data(self, mint_address: str) -> Optional[Dict]:
        """Get current price and bonding curve data for a token"""
        try:
            # Try DexScreener first
            dex_data = await self.get_dexscreener_data(mint_address)
            if dex_data:
                return dex_data
            
            # Fallback to PumpPortal API
            pump_data = await self.get_pumpportal_data(mint_address)
            if pump_data:
                return pump_data
            
            self.logger.warning(f"Could not get price data for {mint_address}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting price data: {e}")
            return None
    
    async def get_dexscreener_data(self, mint_address: str) -> Optional[Dict]:
        """Get price data from DexScreener"""
        try:
            url = f"{self.dexscreener_base}/dex/tokens/{mint_address}"
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    if data.get('pairs'):
                        pair = data['pairs'][0]  # Get first pair
                        return {
                            'price': float(pair.get('priceUsd', 0)),
                            'volume_24h': float(pair.get('volume', {}).get('h24', 0)),
                            'price_change_5m': float(pair.get('priceChange', {}).get('m5', 0)),
                            'liquidity': float(pair.get('liquidity', {}).get('usd', 0)),
                            'source': 'dexscreener'
                        }
                
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting DexScreener data: {e}")
            return None
    
    async def get_pumpportal_data(self, mint_address: str) -> Optional[Dict]:
        """Get price data from PumpPortal API"""
        try:
            # TODO: Implement PumpPortal price API call
            # This is a placeholder implementation
            
            url = f"{self.config['apis']['pump_portal_base_url']}/tokens/{mint_address}/price"
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        'price': float(data.get('price', 0)),
                        'bonding_curve_percent': float(data.get('bonding_curve_pct', 0)),
                        'volume_5m': float(data.get('volume_5m', 0)),
                        'source': 'pumpportal'
                    }
                
                return None
                
        except Exception as e:
            self.logger.error(f"Error getting PumpPortal data: {e}")
            return None
    
    def check_exit_conditions(self, monitor_data: Dict) -> Optional[str]:
        """Check if any exit conditions are met"""
        trade_data = monitor_data['trade_data']
        entry_price = monitor_data['entry_price']
        current_price = monitor_data.get('current_price', 0)
        highest_price = monitor_data['highest_price']
        
        if current_price <= 0:
            return None
        
        # Calculate price change percentage
        price_change_pct = ((current_price - entry_price) / entry_price) * 100
        
        # Check take profit
        tp_min = self.config['exit_strategy']['take_profit_min']
        tp_max = self.config['exit_strategy']['take_profit_max']
        
        if price_change_pct >= tp_min:
            return f"Take profit triggered: +{price_change_pct:.2f}%"
        
        # Check stop loss
        stop_loss = self.config['exit_strategy']['stop_loss']
        if price_change_pct <= stop_loss:
            return f"Stop loss triggered: {price_change_pct:.2f}%"
        
        # Check bonding curve migration risk
        curve_pct = monitor_data.get('bonding_curve_percent', 0)
        curve_exit_threshold = self.config['exit_strategy']['curve_migration_exit']
        
        if curve_pct >= curve_exit_threshold:
            return f"Curve migration risk: {curve_pct:.1f}%"
        
        # Check trailing stop (if enabled)
        if self.config['exit_strategy']['trailing_stop_enabled']:
            trailing_pct = self.config['exit_strategy']['trailing_stop_percent']
            trailing_threshold = highest_price * (1 - trailing_pct / 100)
            
            if current_price <= trailing_threshold:
                return f"Trailing stop triggered: -{trailing_pct}% from peak"
        
        # Check maximum hold time
        max_hold_minutes = self.config['exit_strategy']['max_hold_time_minutes']
        hold_time = datetime.now() - monitor_data['start_time']
        
        if hold_time.total_seconds() / 60 >= max_hold_minutes:
            return f"Max hold time reached: {max_hold_minutes} minutes"
        
        return None
    
    def get_monitoring_stats(self) -> Dict:
        """Get statistics about currently monitored trades"""
        active_count = len(self.active_monitors)
        
        if active_count == 0:
            return {'active_trades': 0}
        
        total_pnl = 0
        winning_trades = 0
        
        for monitor_data in self.active_monitors.values():
            entry_price = monitor_data['entry_price']
            current_price = monitor_data.get('current_price', entry_price)
            
            if current_price > 0:
                pnl_pct = ((current_price - entry_price) / entry_price) * 100
                total_pnl += pnl_pct
                
                if pnl_pct > 0:
                    winning_trades += 1
        
        avg_pnl = total_pnl / active_count if active_count > 0 else 0
        
        return {
            'active_trades': active_count,
            'winning_trades': winning_trades,
            'average_pnl': round(avg_pnl, 2),
            'win_rate': round((winning_trades / active_count) * 100, 1) if active_count > 0 else 0
        }
    
    async def close(self):
        """Stop all monitoring and cleanup"""
        # Stop all active monitors
        for trade_id in list(self.active_monitors.keys()):
            await self.stop_monitoring(trade_id)
        
        # Close session
        if self.session:
            await self.session.close()
