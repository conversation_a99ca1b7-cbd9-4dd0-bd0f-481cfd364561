# AutoSniper - Pump.fun Token Auto-Trading Bot

A sophisticated auto-sniping bot for Pump.fun tokens with real-time scanning, filtering, and automated trading capabilities.

## 🚀 Features

- **Real-time Token Scanning**: Monitors Pump.fun for new tokens via PumpPortal API
- **Advanced Filtering**: Multi-criteria filtering system (bonding curve, volume, holders, etc.)
- **Rugcheck Integration**: Helius API integration for safety checks
- **Automated Trading**: Buy/sell execution with dynamic slippage
- **Price Monitoring**: Real-time price tracking with configurable exit strategies
- **Risk Management**: Stop-loss, take-profit, and position sizing
- **Wallet Management**: Support for multiple wallets and rotation
- **Comprehensive Logging**: SQLite database with detailed trade metrics

## 📋 Requirements

- Python 3.9+
- Solana wallet with SOL for trading
- Helius API key (for rugcheck)
- Minimum 0.1 SOL recommended for testing

## 🛠️ Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd Autosniper
```

2. Run the setup script:
```bash
python setup.py
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Configure your credentials:
```bash
# Edit .env file with your wallet private key and API keys
# WALLET_PRIVATE_KEY=your_base58_private_key_here
# HELIUS_API_KEY=your_helius_api_key_here
```

5. Test the bot:
```bash
python test_bot.py
```

## ⚙️ Configuration

### Key Settings in `config.json`:

- **Trading Parameters**: Bonding curve thresholds, volume filters, trade amounts
- **Exit Strategy**: Take profit (35-50%), stop loss (-20%), curve migration (65%)
- **Safety Checks**: LP lock, ownership renouncement, mint authority
- **Monitoring**: Price check intervals, API rate limits
- **Risk Management**: Max concurrent trades, position sizing

### Environment Variables in `.env`:

- `WALLET_PRIVATE_KEY`: Your Solana wallet private key (base58)
- `HELIUS_API_KEY`: Helius API key for rugcheck
- Additional wallet keys for scaling

## 🚀 Usage

1. **Start the bot**:
```bash
python main.py
```

2. **Monitor logs**:
```bash
tail -f trades.log
```

3. **Check database**:
```bash
# Trade history stored in trades.db (SQLite)
```

## 📊 Expected Performance

| Win Rate | Daily Trades | Net Profit/Day |
|----------|--------------|----------------|
| 20%      | 100          | ~$10           |
| 30%      | 100          | ~$50           |
| 50%+     | 100          | $100+          |

*Results with $5-$15 at risk across 1-3 concurrent trades*

## 🏗️ Architecture

```
autosniperx/
├── config.json          # Strategy parameters
├── .env                 # Private keys & API keys
├── main.py              # Main orchestration loop
├── scanner.py           # PumpPortal token scanner
├── filter.py            # Token filtering logic
├── helius_check.py      # Rugcheck via Helius API
├── executor.py          # Trade execution engine
├── monitor.py           # Price monitoring system
├── wallet.py            # Wallet management
├── logger.py            # Logging & metrics
└── trades.db            # SQLite database
```

## ⚠️ Risk Disclaimer

This bot involves automated cryptocurrency trading which carries significant financial risk. 

- **Start with small amounts** for testing
- **Never invest more than you can afford to lose**
- **Monitor the bot actively** during initial runs
- **Understand that past performance doesn't guarantee future results**

## 🔧 Development

- Set `paper_trading: true` in config.json for testing
- Use `debug_mode: true` for verbose logging
- Test with small amounts before scaling up

## 📝 License

This project is for educational purposes. Use at your own risk.
