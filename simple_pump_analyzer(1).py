"""
SIMPLE PUMP.FUN ANALYZER - DIRECT HELIUS APPROACH
Using your exact functions for super fast and accurate analysis
"""

import requests
import sys
import json
import os
import time
from datetime import datetime
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor
import asyncio
import aiohttp
try:
    import orj<PERSON> as json_fast
    JSON_FAST_AVAILABLE = True
except ImportError:
    import json as json_fast
    JSON_FAST_AVAILABLE = False

# Load environment variables
load_dotenv()

# Load configuration from finalconfig.json
def load_config():
    """Load configuration from finalconfig.json"""
    try:
        with open('finalconfig.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        raise FileNotFoundError("finalconfig.json not found. Please ensure the configuration file exists.")
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON in finalconfig.json: {e}")

# Load the configuration
config = load_config()

# API Keys from environment variables
HELIUS_API_KEY = os.getenv('HELIUS_API_KEY')

# API Endpoints from JSON config
api_endpoints = config['api_endpoints']
RPC_URL = api_endpoints['helius_rpc'].format(api_key=HELIUS_API_KEY)
COINGECKO_SOL_PRICE_URL = api_endpoints['coingecko_sol_price']
# DEXSCREENER_ENDPOINT = api_endpoints['dexscreener']  # Not currently used

# Pump.fun settings from JSON config
pump_settings = config['pump_fun_settings']
DEFAULT_SOL_PRICE = pump_settings['default_sol_price']
PUMP_FUN_GRADUATION_TARGET = pump_settings['graduation_target']
VIRTUAL_LIQUIDITY_MULTIPLIER = pump_settings['virtual_liquidity_multiplier']
# REQUEST_TIMEOUT = pump_settings['request_timeout']  # Not currently used
# REQUEST_HEADERS = pump_settings['request_headers']  # Not currently used

# Display Settings
RESULTS_SEPARATOR = "=" * 60

# Global variable to store DexScreener data for age calculation
_current_dex_data = None

# Caching system for performance optimization
_sol_price_cache = {"price": None, "timestamp": 0}
_token_supply_cache = {}
_session = None

def get_session():
    """Get or create a requests session with connection pooling"""
    global _session
    if _session is None:
        _session = requests.Session()
        # Configure connection pooling for better performance
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=20,
            max_retries=3,
            pool_block=False
        )
        _session.mount('http://', adapter)
        _session.mount('https://', adapter)
        # Set default headers for better performance
        _session.headers.update({
            'Connection': 'keep-alive',
            'Accept-Encoding': 'gzip, deflate',
            'User-Agent': 'PumpAnalyzer/1.0'
        })
    return _session

def validate_config():
    """Validate that all required configuration is present"""
    if not HELIUS_API_KEY:
        raise ValueError("HELIUS_API_KEY not found in environment variables. Please check your .env file.")

    print("✅ Configuration loaded successfully")
    print(f"🔗 Helius RPC: {RPC_URL[:50]}...")
    print(f"🔗 CoinGecko URL: {COINGECKO_SOL_PRICE_URL}")
    print(f"🔗 DexScreener URL: https://api.dexscreener.com/token-pairs/v1/{{chainId}}/{{tokenAddress}}")
    print(f"⚙️ Graduation Target: ${PUMP_FUN_GRADUATION_TARGET:,}")
    print(f"⚙️ Virtual Liquidity Multiplier: {VIRTUAL_LIQUIDITY_MULTIPLIER}")

def get_token_supply(token_address):
    """Get token total supply with permanent caching (supply never changes)"""
    global _token_supply_cache

    # Check cache first (token supply never changes)
    if token_address in _token_supply_cache:
        return _token_supply_cache[token_address]

    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenSupply",
        "params": [token_address]
    }

    session = get_session()
    res = session.post(RPC_URL, json=payload, timeout=3)
    data = res.json()
    supply = int(data['result']['value']['amount'])
    decimals = int(data['result']['value']['decimals'])
    calculated_supply = supply / (10 ** decimals)

    # Cache the result permanently
    _token_supply_cache[token_address] = calculated_supply

    return calculated_supply

def get_pool_balance(pool_address):
    """Get SOL balance from pool address with session optimization"""
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getBalance",
        "params": [pool_address]
    }

    session = get_session()
    res = session.post(RPC_URL, json=payload, timeout=3)
    lamports = int(res.json()['result']['value'])
    return lamports / 1e9  # in SOL

def get_sol_price():
    """Get SOL price with 1-minute caching for optimization"""
    global _sol_price_cache

    current_time = time.time()
    # Check if cached price is still valid (1 minute = 60 seconds)
    if (_sol_price_cache["price"] is not None and
        current_time - _sol_price_cache["timestamp"] < 60):
        return _sol_price_cache["price"]

    try:
        session = get_session()
        res = session.get(COINGECKO_SOL_PRICE_URL, timeout=2)  # Faster timeout with session
        price = res.json()['solana']['usd']

        # Update cache
        _sol_price_cache["price"] = price
        _sol_price_cache["timestamp"] = current_time

        return price
    except (requests.RequestException, KeyError, ValueError):
        # Return cached price if available, otherwise fallback
        if _sol_price_cache["price"] is not None:
            return _sol_price_cache["price"]
        return DEFAULT_SOL_PRICE

def get_total_holders(mint_address, rpc_url, total_supply=None):
    """Get total number of holders for a token using fast estimation"""
    # Use faster approach: get largest accounts first, then full scan only if needed
    try:
        # First try: get largest accounts (fast)
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getTokenLargestAccounts",
            "params": [mint_address]
        }
        session = get_session()
        res = session.post(rpc_url, json=payload, timeout=4).json()

        if res.get("result") and res["result"].get("value"):
            largest_accounts = res["result"]["value"]

            # If we have less than 20 accounts, this is likely the total
            if len(largest_accounts) < 20:
                return len([acc for acc in largest_accounts if int(acc["amount"]) > 0])

            # For tokens with many holders, estimate based on largest accounts
            # This is much faster than full scan
            non_zero_large = len([acc for acc in largest_accounts if int(acc["amount"]) > 0])

            # If all top 20 have tokens, estimate there are more holders
            if non_zero_large == 20:
                # Quick estimation: multiply by factor based on token distribution
                if not total_supply:
                    total_supply = get_token_supply(mint_address)

                # Calculate concentration in top 20
                top_20_total = sum(int(acc["amount"]) for acc in largest_accounts) / (10**6)
                concentration = (top_20_total / total_supply) * 100 if total_supply > 0 else 100

                # Estimate total holders based on concentration
                if concentration > 80:  # Highly concentrated
                    estimated_holders = non_zero_large + 10
                elif concentration > 50:  # Moderately concentrated
                    estimated_holders = non_zero_large * 2
                else:  # Widely distributed
                    estimated_holders = non_zero_large * 5

                return min(estimated_holders, 10000)  # Cap at reasonable number
            else:
                return non_zero_large

        return 0
    except Exception:
        return 0

def print_top_10_summary(token_mint, rpc_url):
    """Print top 10 holders summary"""
    # Use the existing get_token_supply function for consistency
    total_supply_raw = get_token_supply(token_mint)

    # Get holder data using getTokenLargestAccounts
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenLargestAccounts",
        "params": [token_mint]
    }

    try:
        session = get_session()
        res = session.post(rpc_url, json=payload, timeout=10)

        if res.status_code != 200:
            print(f"❌ RPC request failed with status {res.status_code}")
            return

        data = res.json()

        if not data.get("result") or not data["result"].get("value"):
            print(f"❌ Could not fetch holder data - RPC response: {data.get('error', 'Unknown error')}")
            return

        all_holders = data["result"]["value"]

        # Filter out bonding curve pools (typically the largest holder with >8% of supply)
        filtered_holders = []
        for holder in all_holders:
            balance_raw = int(holder["amount"])
            decimals = int(holder.get("decimals", 6))
            balance_adjusted = balance_raw / (10 ** decimals)
            percent = (balance_adjusted / total_supply_raw) * 100

            # Skip if this looks like a bonding curve pool (>8% of supply)
            if percent > 8.0:
                continue

            filtered_holders.append({
                'address': holder['address'],
                'balance': balance_adjusted,
                'percent': percent
            })

        # Take top 10 from filtered list
        top_10_holders = filtered_holders[:10]

        print(f"\n🏆 TOP 10 HOLDERS (excluding pools)")
        print(f"📈 Total Supply: {total_supply_raw:,.0f} tokens")
        print("-" * 80)

        total_top_10_percent = 0
        for i, holder in enumerate(top_10_holders, 1):
            total_top_10_percent += holder['percent']
            print(f"{i:2d}. {holder['address']} – {holder['balance']:,.0f} tokens ({holder['percent']:>6.2f}%)")

        print("-" * 80)
        print(f"🧮 Total % of supply held by top 10 holders: {total_top_10_percent:.2f}%")

    except Exception as e:
        print(f"❌ Error fetching top holders: {e}")
        return

def print_token_age_and_activity(token_mint, rpc_url):
    """Print token age using DexScreener data"""
    global _current_dex_data

    print(f"\n⏰ TOKEN AGE & ACTIVITY")
    print("-" * 50)

    # Get token age from DexScreener data (most accurate)
    creation_time = None
    if _current_dex_data and _current_dex_data.get('pair_created_at'):
        # DexScreener provides pair creation time in milliseconds
        creation_time = _current_dex_data['pair_created_at'] / 1000

    if creation_time:
        current_time = time.time()
        age_seconds = int(current_time - creation_time)

        # Convert to human readable
        if age_seconds < 60:  # Less than 1 minute
            age_str = f"{age_seconds} seconds"
        elif age_seconds < 3600:  # Less than 1 hour
            age_str = f"{age_seconds // 60} minutes"
        elif age_seconds < 86400:  # Less than 1 day
            hours = age_seconds / 3600
            if hours < 2:
                age_str = f"{hours:.1f} hours"
            else:
                age_str = f"{int(round(hours))} hours"
        else:  # 1+ days
            age_str = f"{age_seconds // 86400} days"

        creation_date = datetime.fromtimestamp(creation_time).strftime("%Y-%m-%d %H:%M:%S")
        print(f"🎂 Token Age: {age_str} old")
        print(f"📅 Created: {creation_date}")
    else:
        print("❌ Could not determine token age - DexScreener data not available")

    # Get total holders
    total_holders = get_total_holders(token_mint, rpc_url)
    print(f"👥 Total holders: {total_holders}")

def get_dexscreener_data(token_address):
    """Get token data from DexScreener API with optimized session"""
    try:
        # Use the correct endpoint format: /v1/{chainId}/{tokenAddress}
        url = f"https://api.dexscreener.com/token-pairs/v1/solana/{token_address}"
        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive"
        }

        session = get_session()
        res = session.get(url, headers=headers, timeout=3)  # Faster timeout with session

        if res.status_code != 200:
            return None

        # Use faster JSON parsing if available
        if JSON_FAST_AVAILABLE:
            data = json_fast.loads(res.content)
        else:
            data = res.json()

        # Use the extracted processing function
        return process_dexscreener_response(data)

    except Exception:
        return None

def get_largest_token_accounts(mint_address):
    """Get the largest token accounts for a mint (to find bonding curve pool)"""
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getTokenLargestAccounts",
        "params": [mint_address]
    }
    
    session = get_session()
    res = session.post(RPC_URL, json=payload, timeout=4)
    data = res.json()
    
    if 'result' in data and 'value' in data['result']:
        accounts = data['result']['value']
        return accounts
    else:
        return []

def get_token_account_info(token_account_address):
    """Get detailed information about a token account"""
    payload = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "getAccountInfo",
        "params": [
            token_account_address,
            {"encoding": "jsonParsed"}
        ]
    }
    
    session = get_session()
    res = session.post(RPC_URL, json=payload, timeout=4)
    data = res.json()
    
    if 'result' in data and data['result']['value']:
        account_data = data['result']['value']
        if 'data' in account_data and 'parsed' in account_data['data']:
            parsed_data = account_data['data']['parsed']
            if 'info' in parsed_data:
                info = parsed_data['info']
                owner = info.get('owner')
                mint = info.get('mint')
                amount = info.get('tokenAmount', {}).get('uiAmount', 0)

                return {
                    'owner': owner,
                    'mint': mint,
                    'amount': amount,
                    'address': token_account_address
                }
    
    return None

async def fetch_dexscreener_async(session, token_address):
    """Async version of DexScreener data fetching"""
    try:
        url = f"https://api.dexscreener.com/token-pairs/v1/solana/{token_address}"
        headers = {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Connection": "keep-alive"
        }

        async with session.get(url, headers=headers, timeout=3) as response:
            if response.status != 200:
                return None

            data = await response.json()
            # Process data same as sync version
            return process_dexscreener_response(data)
    except Exception:
        return None

async def fetch_token_supply_async(session, token_address):
    """Async version of token supply fetching"""
    global _token_supply_cache

    # Check cache first
    if token_address in _token_supply_cache:
        return _token_supply_cache[token_address]

    try:
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getTokenSupply",
            "params": [token_address]
        }

        async with session.post(RPC_URL, json=payload, timeout=3) as response:
            data = await response.json()
            supply = int(data['result']['value']['amount'])
            decimals = int(data['result']['value']['decimals'])
            calculated_supply = supply / (10 ** decimals)

            # Cache the result
            _token_supply_cache[token_address] = calculated_supply
            return calculated_supply
    except Exception:
        # Fallback to sync version
        return get_token_supply(token_address)

async def fetch_sol_price_async(session):
    """Async version of SOL price fetching"""
    global _sol_price_cache

    current_time = time.time()
    # Check cache first
    if (_sol_price_cache["price"] is not None and
        current_time - _sol_price_cache["timestamp"] < 60):
        return _sol_price_cache["price"]

    try:
        async with session.get(COINGECKO_SOL_PRICE_URL, timeout=2) as response:
            data = await response.json()
            price = data['solana']['usd']

            # Update cache
            _sol_price_cache["price"] = price
            _sol_price_cache["timestamp"] = current_time

            return price
    except Exception:
        # Return cached or fallback
        if _sol_price_cache["price"] is not None:
            return _sol_price_cache["price"]
        return DEFAULT_SOL_PRICE

def process_dexscreener_response(data):
    """Process DexScreener API response (extracted from main function)"""
    if isinstance(data, list) and len(data) > 0:
        # Smart pair selection logic (same as original)
        selected_pair = None

        # Priority 1: pumpswap
        for pair in data:
            if pair.get('dexId') == 'pumpswap':
                selected_pair = pair
                break

        # Priority 2: pumpfun
        if not selected_pair:
            for pair in data:
                if pair.get('dexId') == 'pumpfun':
                    selected_pair = pair
                    break

        # Priority 3: raydium
        if not selected_pair:
            for pair in data:
                if pair.get('dexId') == 'raydium':
                    selected_pair = pair
                    break

        # Priority 4: highest liquidity
        if not selected_pair:
            pairs_with_liquidity = []
            for pair in data:
                liquidity = pair.get('liquidity', {})
                if isinstance(liquidity, dict):
                    usd_liquidity = liquidity.get('usd', 0)
                else:
                    usd_liquidity = 0
                pairs_with_liquidity.append((pair, usd_liquidity))

            pairs_with_liquidity.sort(key=lambda x: x[1], reverse=True)
            if pairs_with_liquidity:
                selected_pair = pairs_with_liquidity[0][0]

        # Fallback
        if not selected_pair:
            selected_pair = data[0]

        pair = selected_pair

        # Extract data (same as original)
        txns = pair.get('txns', {})
        volume = pair.get('volume', {})
        price_change = pair.get('priceChange', {})
        liquidity = pair.get('liquidity', {})
        liquidity_usd = float(liquidity.get('usd', 0)) if isinstance(liquidity, dict) else 0

        return {
            'dex_id': pair.get('dexId'),
            'pair_address': pair.get('pairAddress'),
            'price_usd': float(pair.get('priceUsd', 0)),
            'price_native': float(pair.get('priceNative', 0)),
            'market_cap': float(pair.get('marketCap', 0)),
            'fdv': float(pair.get('fdv', 0)),
            'liquidity_usd': liquidity_usd,
            'volume_m5': float(volume.get('m5', 0)),
            'volume_h1': float(volume.get('h1', 0)),
            'volume_h6': float(volume.get('h6', 0)),
            'volume_h24': float(volume.get('h24', 0)),
            'price_change_m5': float(price_change.get('m5', 0)),
            'price_change_h1': float(price_change.get('h1', 0)),
            'price_change_h6': float(price_change.get('h6', 0)),
            'price_change_h24': float(price_change.get('h24', 0)),
            'txns_m5_buys': txns.get('m5', {}).get('buys', 0),
            'txns_m5_sells': txns.get('m5', {}).get('sells', 0),
            'txns_h1_buys': txns.get('h1', {}).get('buys', 0),
            'txns_h1_sells': txns.get('h1', {}).get('sells', 0),
            'txns_h6_buys': txns.get('h6', {}).get('buys', 0),
            'txns_h6_sells': txns.get('h6', {}).get('sells', 0),
            'txns_h24_buys': txns.get('h24', {}).get('buys', 0),
            'txns_h24_sells': txns.get('h24', {}).get('sells', 0),
            'pair_created_at': pair.get('pairCreatedAt'),
            'url': pair.get('url', ''),
            'base_token': pair.get('baseToken', {}),
            'quote_token': pair.get('quoteToken', {})
        }
    return None

def find_pump_fun_pool(token_address):
    """Find the pump.fun bonding curve pool address"""
    # Get largest token accounts (bonding curve pool usually holds most tokens)
    largest_accounts = get_largest_token_accounts(token_address)

    if largest_accounts:
        # Use parallel processing to check multiple accounts simultaneously
        def check_account(account):
            try:
                pool_token_account = account['address']
                amount = int(account['amount'])
                decimals = account.get('decimals', 6)
                token_amount = amount / (10 ** decimals)

                # Get the account info to find the owner
                account_info = get_token_account_info(pool_token_account)
                if account_info and account_info.get('owner'):
                    pool_owner = account_info['owner']

                    # Check if this looks like a pump.fun pool
                    sol_balance = get_pool_balance(pool_owner)

                    # If this account has significant SOL and tokens, it's likely the bonding curve
                    if sol_balance > 0.001 and token_amount > 100000:
                        return pool_owner
                return None
            except (requests.RequestException, KeyError, ValueError, TypeError):
                return None

        # Check top 3 accounts in parallel
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(check_account, account) for account in largest_accounts[:3]]

            for future in futures:
                result = future.result()
                if result:
                    return result

        # If no good pool found, use the largest as fallback
        try:
            largest_account = largest_accounts[0]
            pool_token_account = largest_account['address']
            account_info = get_token_account_info(pool_token_account)
            if account_info and account_info.get('owner'):
                return account_info['owner']
        except (requests.RequestException, KeyError, ValueError, TypeError):
            pass

    return None

async def fetch_parallel_data_async(token_address):
    """Fetch DexScreener data, token supply, and SOL price in parallel using async"""
    try:
        async with aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=10),
            connector=aiohttp.TCPConnector(limit=10, limit_per_host=5)
        ) as session:
            # Create async tasks
            tasks = [
                fetch_dexscreener_async(session, token_address),
                fetch_token_supply_async(session, token_address),
                fetch_sol_price_async(session)
            ]

            # Execute all tasks concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Handle results and exceptions
            dex_data = results[0] if not isinstance(results[0], Exception) else None
            supply = results[1] if not isinstance(results[1], Exception) else get_token_supply(token_address)
            sol_price = results[2] if not isinstance(results[2], Exception) else get_sol_price()

            return dex_data, supply, sol_price
    except Exception:
        # Fallback to synchronous version
        return fetch_parallel_data_sync(token_address)

def fetch_parallel_data_sync(token_address):
    """Synchronous fallback for parallel data fetching"""
    with ThreadPoolExecutor(max_workers=3) as executor:
        # Submit all tasks simultaneously
        dex_future = executor.submit(get_dexscreener_data, token_address)
        supply_future = executor.submit(get_token_supply, token_address)
        sol_price_future = executor.submit(get_sol_price)

        # Get results
        dex_data = dex_future.result()
        supply = supply_future.result()
        sol_price = sol_price_future.result()

        return dex_data, supply, sol_price

def fetch_parallel_data(token_address):
    """Fetch data using async if possible, fallback to sync"""
    try:
        # Check if we're already in an event loop
        try:
            asyncio.get_running_loop()
            # If we're in a loop, use sync version to avoid conflicts
            return fetch_parallel_data_sync(token_address)
        except RuntimeError:
            # No running loop, safe to create new one
            pass

        # Create and run async version
        return asyncio.run(fetch_parallel_data_async(token_address))
    except Exception as e:
        # Fallback to synchronous version
        print(f"⚠️ Async failed ({e}), using sync fallback")
        return fetch_parallel_data_sync(token_address)

def calculate_market_metrics(token_address, pool_address=None):
    """Calculate all market metrics for a pump.fun token"""
    global _current_dex_data

    print(f"⚡ ANALYZING TOKEN: {token_address}")
    print("=" * 60)

    # Fetch core data in parallel for speed
    dex_data, supply, sol_price = fetch_parallel_data(token_address)
    _current_dex_data = dex_data  # Store for age calculation

    print(f"📊 Token Supply: {supply:,.0f} tokens")
    print(f"💲 Current SOL Price: ${sol_price:.2f}")

    # Always use DexScreener data when available for price and market cap
    if dex_data:
        print(f"\n🎯 Using DexScreener data for price and market cap")

        # Use DexScreener data directly
        market_cap = dex_data['market_cap']
        token_price = dex_data['price_usd']
        token_price_sol = dex_data['price_native']

        print(f"📊 Market Cap: ${market_cap:,.2f} (from DexScreener)")
        print(f"💰 FDV: ${dex_data['fdv']:,.2f} (from DexScreener)")
        print(f"💵 Token Price: ${token_price:.8f} ({token_price_sol:.8f} SOL) (from DexScreener)")

        # Get pool SOL from Helius for additional context
        if not pool_address:
            pool_address = find_pump_fun_pool(token_address)

        if pool_address:
            pool_sol = get_pool_balance(pool_address)
            print(f"💰 Pool SOL: {pool_sol:.6f} SOL (from Helius)")

            # Use DexScreener liquidity if market cap > $80k, otherwise calculate liquidity
            if market_cap > 80000:
                dex_liquidity = dex_data.get('liquidity_usd', 0)
                if dex_liquidity > 0:
                    liquidity_usd = dex_liquidity
                    print(f"💧 Liquidity: ${liquidity_usd:,.0f} (from DexScreener)")
                else:
                    liquidity_usd = pool_sol * sol_price * VIRTUAL_LIQUIDITY_MULTIPLIER
                    print(f"💧 Liquidity: ${liquidity_usd:.2f} (calculated ×{VIRTUAL_LIQUIDITY_MULTIPLIER})")
            else:
                liquidity_usd = pool_sol * sol_price * VIRTUAL_LIQUIDITY_MULTIPLIER
                print(f"💧 Liquidity: ${liquidity_usd:.2f} (calculated ×{VIRTUAL_LIQUIDITY_MULTIPLIER})")

            print(f"📊 Market Cap vs Liquidity: ${market_cap:,.0f} vs ${liquidity_usd:,.0f}")
        else:
            pool_sol = 0
            liquidity_usd = 0
            print("⚠️ Could not find pool address for SOL balance")

        # Display volume data for all time periods
        print(f"\n📈 Volume Data:")
        print(f"   5m: ${dex_data['volume_m5']:,.0f}")
        print(f"   1h: ${dex_data['volume_h1']:,.0f}")
        print(f"   6h: ${dex_data['volume_h6']:,.0f}")
        print(f"   24h: ${dex_data['volume_h24']:,.0f}")

        # Display price change data for all time periods
        print(f"\n📊 Price Changes:")
        print(f"   5m: {dex_data['price_change_m5']:+.1f}%")
        print(f"   1h: {dex_data['price_change_h1']:+.1f}%")
        print(f"   6h: {dex_data['price_change_h6']:+.1f}%")
        print(f"   24h: {dex_data['price_change_h24']:+.1f}%")

        # Display transaction data for all time periods
        print(f"\n🔄 Transaction Data:")
        print(f"   5m: {dex_data['txns_m5_buys']} buys, {dex_data['txns_m5_sells']} sells")
        print(f"   1h: {dex_data['txns_h1_buys']} buys, {dex_data['txns_h1_sells']} sells")
        print(f"   6h: {dex_data['txns_h6_buys']} buys, {dex_data['txns_h6_sells']} sells")
        print(f"   24h: {dex_data['txns_h24_buys']} buys, {dex_data['txns_h24_sells']} sells")

    else:
        # Fallback: Use Helius + pump.fun calculation when DexScreener data not available
        print(f"\n⚠️ DexScreener data not available - Using Helius RPC + Pump.fun calculation")

        # Find pool if not provided
        if not pool_address:
            pool_address = find_pump_fun_pool(token_address)
            if not pool_address:
                print("❌ Could not find pool address")
                return None

        # Get pool SOL balance
        pool_sol = get_pool_balance(pool_address)
        print(f"💰 Pool SOL Balance: {pool_sol:.6f} SOL")

        # Validate pool SOL balance
        if pool_sol <= 0:
            print("⚠️ Warning: Pool SOL balance is 0 or negative - this might not be the correct pool")
        elif pool_sol > 100:
            print("⚠️ Warning: Pool SOL balance seems very high - this might not be a pump.fun bonding curve")

        # Calculate metrics using pump.fun formulas
        # Liquidity calculation: SOL Price × Pool SOL × multiplier
        liquidity_usd = pool_sol * sol_price * VIRTUAL_LIQUIDITY_MULTIPLIER

        # PUMP.FUN EXACT FORMULA:
        # The liquidity IS the market cap for pump.fun tokens
        # This is because pump.fun uses the liquidity as the market cap display
        market_cap = liquidity_usd

        # Calculate token price from market cap
        # Price = Market Cap ÷ Total Supply (1B tokens)
        token_price = market_cap / supply if supply > 0 else 0
        token_price_sol = token_price / sol_price if sol_price > 0 else 0

        print(f"🧮 Calculation Debug:")
        print(f"   Liquidity = {pool_sol:.6f} SOL × ${sol_price:.2f} × {VIRTUAL_LIQUIDITY_MULTIPLIER} = ${liquidity_usd:.2f}")
        print(f"   Market Cap = Liquidity = ${market_cap:.2f}")
        print(f"   Token Price = ${market_cap:.2f} ÷ {supply:,.0f} = ${token_price:.8f}")
        print(f"   Token Price SOL = ${token_price:.8f} ÷ ${sol_price:.2f} = {token_price_sol:.8f} SOL")

    # Calculate bonding curve progress
    graduation_target = PUMP_FUN_GRADUATION_TARGET
    bonding_progress = min((market_cap / graduation_target) * 100, 100)

    # Validate results
    if market_cap > graduation_target:
        print("⚠️ Warning: Market cap exceeds graduation target - token might have already graduated")
    elif market_cap < 1:
        print("⚠️ Warning: Market cap is very low - check if pool address is correct")
    
    # Prepare results dictionary
    results = {
        "Token Address": token_address,
        "Pool Address": pool_address,
        "Total Supply": supply,
        "Pool SOL": pool_sol,
        "SOL Price": sol_price,
        "Liquidity USD": liquidity_usd,
        "Token Price (USD)": token_price,
        "Token Price (SOL)": token_price_sol,
        "Market Cap (USD)": market_cap,
        "Bonding Progress": bonding_progress,
        "Graduation Target": graduation_target,
        "Graduation Remaining": max(graduation_target - market_cap, 0)
    }

    # Add DexScreener data if available
    if dex_data:
        results["DexScreener Data"] = {
            "dex_id": dex_data['dex_id'],
            "fdv": dex_data['fdv'],
            "volume": {
                "m5": dex_data['volume_m5'],
                "h1": dex_data['volume_h1'],
                "h6": dex_data['volume_h6'],
                "h24": dex_data['volume_h24']
            },
            "price_change": {
                "m5": dex_data['price_change_m5'],
                "h1": dex_data['price_change_h1'],
                "h6": dex_data['price_change_h6'],
                "h24": dex_data['price_change_h24']
            },
            "transactions": {
                "m5": {"buys": dex_data['txns_m5_buys'], "sells": dex_data['txns_m5_sells']},
                "h1": {"buys": dex_data['txns_h1_buys'], "sells": dex_data['txns_h1_sells']},
                "h6": {"buys": dex_data['txns_h6_buys'], "sells": dex_data['txns_h6_sells']},
                "h24": {"buys": dex_data['txns_h24_buys'], "sells": dex_data['txns_h24_sells']}
            }
        }

    return results

def display_results(results):
    """Display analysis results"""
    if not results:
        print("❌ No results to display")
        return

    print("\n✅ PUMP.FUN ANALYSIS RESULTS:")
    print(RESULTS_SEPARATOR)
    print(f"🎯 Token: {results['Token Address']}")
    print(f"🏊 Pool: {results['Pool Address']}")
    print()
    print(f"📊 Total Supply: {results['Total Supply']:,.0f} tokens")
    print(f"💰 Pool SOL: {results['Pool SOL']:.6f} SOL")
    print(f"💲 SOL Price: ${results['SOL Price']:.2f}")
    print()
    print(f"💵 Token Price: ${results['Token Price (USD)']:.8f} ({results['Token Price (SOL)']:.8f} SOL)")
    print(f"📈 Market Cap: ${results['Market Cap (USD)']:,.0f}")
    print(f"💧 Liquidity: ${results['Liquidity USD']:,.0f}")
    print()
    print(f"📊 Bonding Progress: {results['Bonding Progress']:.1f}%")
    print(f"🎯 To Graduate: ${results['Graduation Remaining']:,.0f}")

    # Display DexScreener data if available
    if "DexScreener Data" in results:
        dex_data = results["DexScreener Data"]
        print("\n📊 DEXSCREENER DATA:")
        print("-" * 40)
        print(f"🏪 DEX: {dex_data['dex_id']}")
        print(f"💰 FDV: ${dex_data['fdv']:,.0f}")

        print(f"\n📈 Volume:")
        print(f"   5m: ${dex_data['volume']['m5']:,.0f}")
        print(f"   1h: ${dex_data['volume']['h1']:,.0f}")
        print(f"   6h: ${dex_data['volume']['h6']:,.0f}")
        print(f"   24h: ${dex_data['volume']['h24']:,.0f}")

        print(f"\n📊 Price Changes:")
        print(f"   5m: {dex_data['price_change']['m5']:+.1f}%")
        print(f"   1h: {dex_data['price_change']['h1']:+.1f}%")
        print(f"   6h: {dex_data['price_change']['h6']:+.1f}%")
        print(f"   24h: {dex_data['price_change']['h24']:+.1f}%")

        print(f"\n🔄 Transactions:")
        for period in ['m5', 'h1', 'h6', 'h24']:
            period_name = {'m5': '5m', 'h1': '1h', 'h6': '6h', 'h24': '24h'}[period]
            buys = dex_data['transactions'][period]['buys']
            sells = dex_data['transactions'][period]['sells']
            total = buys + sells
            buy_ratio = (buys / total * 100) if total > 0 else 0
            print(f"   {period_name}: {buys} buys, {sells} sells ({buy_ratio:.0f}% buys)")

    print(RESULTS_SEPARATOR)

def main():
    """Main function"""
    print("SIMPLE PUMP.FUN ANALYZER")
    print("Features: DexScreener + Helius RPC, Real-time data, Super fast")
    print()

    # Validate configuration
    try:
        validate_config()
        print()
    except ValueError as e:
        print(f"❌ Configuration Error: {e}")
        return

    # Check if token address provided as argument
    if len(sys.argv) >= 2:
        if sys.argv[1] in ['--help', '-h', 'help']:
            print("💡 Usage:")
            print("   python simple_pump_analyzer.py <token_address> [pool_address]")
            print("   python simple_pump_analyzer.py  # Interactive mode")
            print()
            print("📊 Features:")
            print("   • Always uses DexScreener data for price and market cap when available")
            print("   • Helius RPC for pool SOL balance and fallback calculations")
            print("   • Complete volume, price changes, and transaction data")
            print("   • Automatic pump.fun pool detection")
            print("   • Bonding curve progress calculation")
            print("   • Top 10 holders analysis")
            print("   • Token age (creation time) tracking")
            print("   • Total holders count")
            print("   • Time period data: 5m, 1h, 6h, 24h")
            return

        token_address = sys.argv[1]
        pool_address = sys.argv[2] if len(sys.argv) > 2 else None
    else:
        # Interactive mode - ask for token address
        print("💡 Usage Options:")
        print("   1. Command line: python simple_pump_analyzer.py <token_address> [pool_address]")
        print("   2. Interactive: Just run the script and enter token address when prompted")
        print()

        # Ask for token address
        token_address = input("🎯 Enter Token Address (CA): ").strip()

        if not token_address:
            print("❌ No token address provided. Exiting.")
            return

        # Auto-detect pool address
        pool_address = None

        print()

    try:
        print(f"🔍 Analyzing: {token_address}")
        if pool_address:
            print(f"🏊 Using Pool: {pool_address}")
        print()

        results = calculate_market_metrics(token_address, pool_address)
        display_results(results)

        # Run holder analysis and age analysis in parallel for speed
        with ThreadPoolExecutor(max_workers=2) as executor:
            holders_future = executor.submit(print_top_10_summary, token_address, RPC_URL)
            age_future = executor.submit(print_token_age_and_activity, token_address, RPC_URL)

            # Wait for both to complete
            holders_future.result()
            age_future.result()

        # Ask if user wants to analyze another token
        if len(sys.argv) < 2:  # Only in interactive mode
            print()
            another = input("🔄 Analyze another token? (y/n): ").strip().lower()
            if another in ['y', 'yes']:
                print("\n" + "="*60 + "\n")
                main()  # Recursive call for another analysis

    except KeyboardInterrupt:
        print("\n👋 Analysis cancelled by user")
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

        # In interactive mode, offer to try again
        if len(sys.argv) < 2:
            retry = input("🔄 Try again? (y/n): ").strip().lower()
            if retry in ['y', 'yes']:
                print("\n" + "="*60 + "\n")
                main()

if __name__ == "__main__":
    main()
