"""
Wallet Manager - Handle multiple wallets and rotation
Supports wallet scaling and rotation for increased trading capacity
"""

import logging
import os
import base58
from typing import Dict, List, Optional
from solana.keypair import Keypair
from solana.rpc.async_api import AsyncClient
from solana.rpc.commitment import Confirmed

class WalletManager:
    """Manages multiple wallets for trading operations"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.wallets = []
        self.current_wallet_index = 0
        self.rpc_client = None
        
        # Initialize RPC client
        self.setup_rpc_client()
        
        # Load wallets from environment
        self.load_wallets()
    
    def setup_rpc_client(self):
        """Initialize Solana RPC client"""
        rpc_url = self.config['apis']['solana_rpc_url']
        self.rpc_client = AsyncClient(rpc_url)
    
    def load_wallets(self):
        """Load wallet keypairs from environment variables"""
        try:
            # Load primary wallet
            primary_key = os.getenv('WALLET_PRIVATE_KEY')
            if not primary_key:
                raise ValueError("WALLET_PRIVATE_KEY not found in environment")
            
            primary_wallet = self.create_wallet_from_key(primary_key, 0)
            self.wallets.append(primary_wallet)
            
            # Load additional wallets if available
            max_wallets = self.config['wallet']['max_wallets']
            for i in range(2, max_wallets + 2):  # Start from 2 since primary is 1
                key_var = f'WALLET_PRIVATE_KEY_{i}'
                private_key = os.getenv(key_var)
                
                if private_key:
                    wallet = self.create_wallet_from_key(private_key, i-1)
                    self.wallets.append(wallet)
                    self.logger.info(f"Loaded wallet {i}: {wallet['address']}")
            
            self.logger.info(f"Loaded {len(self.wallets)} wallet(s)")
            
        except Exception as e:
            self.logger.error(f"Failed to load wallets: {e}")
            raise
    
    def create_wallet_from_key(self, private_key: str, index: int) -> Dict:
        """Create wallet object from private key"""
        try:
            # Decode base58 private key
            private_key_bytes = base58.b58decode(private_key)
            keypair = Keypair.from_bytes(private_key_bytes)
            
            return {
                'index': index,
                'keypair': keypair,
                'address': str(keypair.pubkey()),
                'private_key': private_key,
                'balance': 0.0,
                'active_trades': 0,
                'last_used': None,
                'status': 'active'
            }
            
        except Exception as e:
            self.logger.error(f"Error creating wallet from key: {e}")
            raise
    
    def get_current_wallet(self) -> Dict:
        """Get the currently active wallet"""
        if not self.wallets:
            raise ValueError("No wallets available")
        
        return self.wallets[self.current_wallet_index]
    
    def get_next_wallet(self) -> Dict:
        """Get next wallet in rotation"""
        if not self.config['wallet']['wallet_rotation_enabled']:
            return self.get_current_wallet()
        
        if len(self.wallets) <= 1:
            return self.get_current_wallet()
        
        # Find wallet with lowest active trades
        best_wallet = min(self.wallets, key=lambda w: w['active_trades'])
        
        # Update current wallet index
        self.current_wallet_index = best_wallet['index']
        
        return best_wallet
    
    def get_wallet_by_index(self, index: int) -> Optional[Dict]:
        """Get wallet by index"""
        for wallet in self.wallets:
            if wallet['index'] == index:
                return wallet
        return None
    
    async def update_wallet_balances(self):
        """Update SOL balances for all wallets"""
        for wallet in self.wallets:
            try:
                balance = await self.get_wallet_balance(wallet['keypair'])
                wallet['balance'] = balance
                
            except Exception as e:
                self.logger.error(f"Error updating balance for wallet {wallet['address']}: {e}")
                wallet['balance'] = 0.0
    
    async def get_wallet_balance(self, keypair: Keypair) -> float:
        """Get SOL balance for a specific wallet"""
        try:
            response = await self.rpc_client.get_balance(
                keypair.pubkey(),
                commitment=Confirmed
            )
            
            if response.value is not None:
                # Convert lamports to SOL
                balance_sol = response.value / 1_000_000_000
                return balance_sol
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"Error getting wallet balance: {e}")
            return 0.0
    
    def get_available_wallets(self) -> List[Dict]:
        """Get wallets that are available for trading"""
        available = []
        balance_threshold = self.config['wallet']['balance_threshold_sol']
        
        for wallet in self.wallets:
            if (wallet['status'] == 'active' and 
                wallet['balance'] >= balance_threshold):
                available.append(wallet)
        
        return available
    
    def allocate_wallet_for_trade(self) -> Optional[Dict]:
        """Allocate a wallet for a new trade"""
        available_wallets = self.get_available_wallets()
        
        if not available_wallets:
            self.logger.warning("No wallets available for trading")
            return None
        
        # Select wallet with lowest active trades
        selected_wallet = min(available_wallets, key=lambda w: w['active_trades'])
        
        # Increment active trades counter
        selected_wallet['active_trades'] += 1
        selected_wallet['last_used'] = os.times().elapsed
        
        self.logger.info(f"Allocated wallet {selected_wallet['address']} for trade")
        return selected_wallet
    
    def release_wallet_from_trade(self, wallet_address: str):
        """Release a wallet from a completed trade"""
        for wallet in self.wallets:
            if wallet['address'] == wallet_address:
                if wallet['active_trades'] > 0:
                    wallet['active_trades'] -= 1
                self.logger.info(f"Released wallet {wallet_address} from trade")
                break
    
    def get_total_balance(self) -> float:
        """Get total SOL balance across all wallets"""
        return sum(wallet['balance'] for wallet in self.wallets)
    
    def get_wallet_stats(self) -> Dict:
        """Get statistics about wallet usage"""
        total_wallets = len(self.wallets)
        active_wallets = len([w for w in self.wallets if w['status'] == 'active'])
        total_balance = self.get_total_balance()
        total_active_trades = sum(w['active_trades'] for w in self.wallets)
        
        available_wallets = len(self.get_available_wallets())
        
        return {
            'total_wallets': total_wallets,
            'active_wallets': active_wallets,
            'available_wallets': available_wallets,
            'total_balance': round(total_balance, 4),
            'total_active_trades': total_active_trades,
            'current_wallet': self.current_wallet_index
        }
    
    def validate_wallet_setup(self) -> bool:
        """Validate that wallet setup is correct"""
        if not self.wallets:
            self.logger.error("No wallets loaded")
            return False
        
        # Check minimum balance requirements
        balance_threshold = self.config['wallet']['balance_threshold_sol']
        reserve_sol = self.config['wallet']['reserve_sol']
        min_required = balance_threshold + reserve_sol
        
        sufficient_wallets = 0
        for wallet in self.wallets:
            if wallet['balance'] >= min_required:
                sufficient_wallets += 1
        
        if sufficient_wallets == 0:
            self.logger.error(f"No wallets have sufficient balance (minimum: {min_required} SOL)")
            return False
        
        self.logger.info(f"Wallet validation passed: {sufficient_wallets}/{len(self.wallets)} wallets ready")
        return True
    
    def add_wallet(self, private_key: str) -> bool:
        """Add a new wallet to the manager"""
        try:
            index = len(self.wallets)
            wallet = self.create_wallet_from_key(private_key, index)
            self.wallets.append(wallet)
            
            self.logger.info(f"Added new wallet: {wallet['address']}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to add wallet: {e}")
            return False
    
    def remove_wallet(self, wallet_address: str) -> bool:
        """Remove a wallet from the manager"""
        for i, wallet in enumerate(self.wallets):
            if wallet['address'] == wallet_address:
                if wallet['active_trades'] > 0:
                    self.logger.error(f"Cannot remove wallet {wallet_address}: has active trades")
                    return False
                
                del self.wallets[i]
                self.logger.info(f"Removed wallet: {wallet_address}")
                
                # Adjust current wallet index if necessary
                if self.current_wallet_index >= len(self.wallets):
                    self.current_wallet_index = 0
                
                return True
        
        self.logger.error(f"Wallet {wallet_address} not found")
        return False
    
    async def close(self):
        """Close RPC client connection"""
        if self.rpc_client:
            await self.rpc_client.close()
