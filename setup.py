#!/usr/bin/env python3
"""
Setup script for AutoSniper bot
Helps users configure the bot for first-time use
"""

import os
import json
import sys
from pathlib import Path

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    env_path = Path('.env')
    template_path = Path('.env.template')
    
    if env_path.exists():
        print("✅ .env file already exists")
        return True
    
    if not template_path.exists():
        print("❌ .env.template not found")
        return False
    
    # Copy template to .env
    with open(template_path, 'r') as template:
        content = template.read()
    
    with open(env_path, 'w') as env_file:
        env_file.write(content)
    
    print("✅ Created .env file from template")
    print("⚠️  Please edit .env file with your wallet private key and API keys")
    return True

def validate_config():
    """Validate config.json file"""
    config_path = Path('config.json')
    
    if not config_path.exists():
        print("❌ config.json not found")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check required sections
        required_sections = ['trading', 'exit_strategy', 'monitoring', 'safety', 'apis']
        missing_sections = [section for section in required_sections if section not in config]
        
        if missing_sections:
            print(f"❌ Missing config sections: {missing_sections}")
            return False
        
        print("✅ config.json is valid")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in config.json: {e}")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'solana-py',
        'aiohttp',
        'python-dotenv',
        'base58'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {missing_packages}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All required packages are installed")
    return True

def check_environment_variables():
    """Check if required environment variables are set"""
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = ['WALLET_PRIVATE_KEY', 'HELIUS_API_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        print("Please edit .env file with your credentials")
        return False
    
    print("✅ All required environment variables are set")
    return True

def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'data', 'exports']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Created necessary directories")

def display_setup_instructions():
    """Display setup instructions"""
    print("\n" + "="*60)
    print("🚀 AutoSniper Setup Instructions")
    print("="*60)
    print()
    print("1. Edit .env file with your credentials:")
    print("   - WALLET_PRIVATE_KEY: Your Solana wallet private key (base58)")
    print("   - HELIUS_API_KEY: Your Helius API key for rugcheck")
    print()
    print("2. Customize config.json for your trading strategy:")
    print("   - Adjust bonding curve thresholds")
    print("   - Set take profit and stop loss levels")
    print("   - Configure trade amounts and slippage")
    print()
    print("3. Test with small amounts first:")
    print("   - Set trade_amount_sol to 0.01 or less")
    print("   - Enable paper_trading mode for testing")
    print()
    print("4. Start the bot:")
    print("   python main.py")
    print()
    print("⚠️  IMPORTANT WARNINGS:")
    print("   - This bot involves real money trading")
    print("   - Start with small amounts")
    print("   - Monitor the bot actively")
    print("   - Understand the risks involved")
    print()

def main():
    """Main setup function"""
    print("🔧 AutoSniper Bot Setup")
    print("=" * 30)
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Python 3.9+ required")
        sys.exit(1)
    
    print("✅ Python version OK")
    
    # Run setup checks
    steps = [
        ("Checking dependencies", check_dependencies),
        ("Creating .env file", create_env_file),
        ("Validating config.json", validate_config),
        ("Creating directories", create_directories),
        ("Checking environment variables", check_environment_variables)
    ]
    
    all_passed = True
    
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            if not step_func():
                all_passed = False
        except Exception as e:
            print(f"❌ Error in {step_name}: {e}")
            all_passed = False
    
    print("\n" + "="*30)
    
    if all_passed:
        print("✅ Setup completed successfully!")
        display_setup_instructions()
    else:
        print("❌ Setup incomplete. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
