#!/usr/bin/env python3
"""
Test script for AutoSniper bot components
Tests individual components and validates the complete system
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from dotenv import load_dotenv

# Import bot components
from scanner import TokenScanner
from filter import Token<PERSON>ilter
from helius_check import <PERSON><PERSON><PERSON><PERSON><PERSON>
from executor import TradeExecutor
from monitor import PriceMonitor
from wallet import WalletManager
from logger import TradeLogger

class BotTester:
    """Test suite for AutoSniper bot"""
    
    def __init__(self):
        # Load configuration
        with open('config.json', 'r') as f:
            self.config = json.load(f)
        
        # Enable test mode
        self.config['development']['test_mode'] = True
        self.config['development']['paper_trading'] = True
        
        load_dotenv()
        
        self.test_results = {}
    
    async def test_wallet_manager(self):
        """Test wallet management functionality"""
        print("🧪 Testing Wallet Manager...")
        
        try:
            wallet_manager = WalletManager(self.config)
            
            # Test wallet loading
            assert len(wallet_manager.wallets) > 0, "No wallets loaded"
            
            # Test balance updates
            await wallet_manager.update_wallet_balances()
            
            # Test wallet stats
            stats = wallet_manager.get_wallet_stats()
            assert 'total_wallets' in stats, "Missing wallet stats"
            
            await wallet_manager.close()
            
            self.test_results['wallet_manager'] = {'status': 'PASS', 'message': 'All tests passed'}
            print("✅ Wallet Manager tests passed")
            
        except Exception as e:
            self.test_results['wallet_manager'] = {'status': 'FAIL', 'message': str(e)}
            print(f"❌ Wallet Manager tests failed: {e}")
    
    async def test_trade_logger(self):
        """Test trade logging functionality"""
        print("🧪 Testing Trade Logger...")
        
        try:
            logger = TradeLogger(self.config)
            
            # Test trade entry logging
            test_trade_data = {
                'mint_address': 'test_mint_123',
                'token_name': 'Test Token',
                'token_symbol': 'TEST',
                'wallet_address': 'test_wallet_123',
                'entry_timestamp': datetime.now().isoformat(),
                'entry_price': 0.000001,
                'entry_amount_sol': 0.01,
                'token_amount': 10000,
                'bonding_curve_percent': 15.5,
                'volume_5m': 2500,
                'txn_count_5m': 150,
                'holders': 45
            }
            
            trade_id = logger.log_trade_entry(test_trade_data)
            assert trade_id, "Failed to log trade entry"
            
            # Test trade exit logging
            exit_data = {
                'exit_timestamp': datetime.now().isoformat(),
                'exit_price': 0.0000015,
                'exit_amount_sol': 0.015,
                'exit_reason': 'Take profit'
            }
            
            logger.log_trade_exit(trade_id, exit_data)
            
            # Test performance stats
            stats = logger.get_performance_stats(days=1)
            assert 'total_trades' in stats, "Missing performance stats"
            
            self.test_results['trade_logger'] = {'status': 'PASS', 'message': 'All tests passed'}
            print("✅ Trade Logger tests passed")
            
        except Exception as e:
            self.test_results['trade_logger'] = {'status': 'FAIL', 'message': str(e)}
            print(f"❌ Trade Logger tests failed: {e}")
    
    async def test_token_filter(self):
        """Test token filtering functionality"""
        print("🧪 Testing Token Filter...")
        
        try:
            filter_engine = TokenFilter(self.config)
            
            # Create test tokens
            test_tokens = [
                {
                    'mint_address': 'good_token_123',
                    'bonding_curve_percent': 25,
                    'volume_5m': 5000,
                    'txn_count_5m': 200,
                    'holders': 50,
                    'market_cap': 25000,
                    'top_holder_percent': 8,
                    'dev_buy_percent': 5
                },
                {
                    'mint_address': 'bad_token_456',
                    'bonding_curve_percent': 2,  # Too low
                    'volume_5m': 500,  # Too low
                    'txn_count_5m': 50,  # Too low
                    'holders': 10,  # Too low
                    'market_cap': 500,  # Too low
                    'top_holder_percent': 25,  # Too high
                    'dev_buy_percent': 25  # Too high
                }
            ]
            
            # Test filtering
            filtered = filter_engine.apply_filters(test_tokens)
            
            # Good token should pass, bad token should be filtered out
            assert len(filtered) == 1, f"Expected 1 filtered token, got {len(filtered)}"
            assert filtered[0]['mint_address'] == 'good_token_123', "Wrong token passed filter"
            
            self.test_results['token_filter'] = {'status': 'PASS', 'message': 'All tests passed'}
            print("✅ Token Filter tests passed")
            
        except Exception as e:
            self.test_results['token_filter'] = {'status': 'FAIL', 'message': str(e)}
            print(f"❌ Token Filter tests failed: {e}")
    
    async def test_scanner_connection(self):
        """Test scanner API connection"""
        print("🧪 Testing Scanner Connection...")
        
        try:
            async with TokenScanner(self.config) as scanner:
                # Test basic connection (this will likely fail without real API)
                tokens = await scanner.get_new_tokens()
                
                # In test mode, we expect empty list or connection error
                # This is mainly to test the structure
                assert isinstance(tokens, list), "Scanner should return a list"
            
            self.test_results['scanner'] = {'status': 'PASS', 'message': 'Connection test passed'}
            print("✅ Scanner connection test passed")
            
        except Exception as e:
            # Expected to fail without real API access
            self.test_results['scanner'] = {'status': 'SKIP', 'message': f'API not available: {str(e)}'}
            print(f"⚠️  Scanner test skipped (API not available): {e}")
    
    async def test_rugcheck_connection(self):
        """Test rugcheck API connection"""
        print("🧪 Testing Rugcheck Connection...")
        
        try:
            if not os.getenv('HELIUS_API_KEY'):
                raise ValueError("HELIUS_API_KEY not set")
            
            async with RugChecker(self.config) as checker:
                # Test with a known token (this will likely fail without real API)
                result = await checker.check_token_safety('test_mint_address')
                
                assert isinstance(result, dict), "Rugcheck should return a dict"
                assert 'safe' in result, "Rugcheck result should have 'safe' field"
            
            self.test_results['rugcheck'] = {'status': 'PASS', 'message': 'Connection test passed'}
            print("✅ Rugcheck connection test passed")
            
        except Exception as e:
            # Expected to fail without real API access
            self.test_results['rugcheck'] = {'status': 'SKIP', 'message': f'API not available: {str(e)}'}
            print(f"⚠️  Rugcheck test skipped (API not available): {e}")
    
    def print_test_summary(self):
        """Print test results summary"""
        print("\n" + "="*50)
        print("🧪 TEST RESULTS SUMMARY")
        print("="*50)
        
        passed = 0
        failed = 0
        skipped = 0
        
        for component, result in self.test_results.items():
            status = result['status']
            message = result['message']
            
            if status == 'PASS':
                print(f"✅ {component}: PASSED")
                passed += 1
            elif status == 'FAIL':
                print(f"❌ {component}: FAILED - {message}")
                failed += 1
            elif status == 'SKIP':
                print(f"⚠️  {component}: SKIPPED - {message}")
                skipped += 1
        
        print(f"\nSummary: {passed} passed, {failed} failed, {skipped} skipped")
        
        if failed > 0:
            print("\n❌ Some tests failed. Please fix the issues before running the bot.")
            return False
        else:
            print("\n✅ All critical tests passed! Bot is ready to run.")
            return True
    
    async def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting AutoSniper Bot Tests")
        print("="*40)
        
        # Run tests
        await self.test_wallet_manager()
        await self.test_trade_logger()
        await self.test_token_filter()
        await self.test_scanner_connection()
        await self.test_rugcheck_connection()
        
        # Print summary
        return self.print_test_summary()

async def main():
    """Main test function"""
    try:
        tester = BotTester()
        success = await tester.run_all_tests()
        
        if success:
            print("\n🎉 Ready to start trading!")
            print("Run: python main.py")
        else:
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
