"""
Helius Rugcheck - Safety validation using Helius API
Checks LP lock, ownership renouncement, mint authority, and other safety factors
"""

import asyncio
import aiohttp
import logging
import os
from typing import Dict, Optional, List

class RugChecker:
    """Performs safety checks using Helius API"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.api_key = os.getenv('HELIUS_API_KEY')
        self.base_url = config['apis']['helius_base_url']
        self.session = None
        
        if not self.api_key:
            self.logger.error("HELIUS_API_KEY not found in environment variables")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.config['monitoring']['timeout_seconds'])
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def check_token_safety(self, mint_address: str) -> Dict:
        """
        Comprehensive safety check for a token
        Returns dict with safety status and details
        """
        if not self.config['safety']['enable_rugcheck']:
            return {'safe': True, 'reason': 'Rugcheck disabled'}
        
        try:
            # Get token metadata and ownership info
            token_info = await self.get_token_info(mint_address)
            if not token_info:
                return {'safe': False, 'reason': 'Could not fetch token info'}
            
            safety_checks = {
                'lp_locked': await self.check_lp_locked(mint_address),
                'ownership_renounced': await self.check_ownership_renounced(mint_address),
                'mint_authority_burned': await self.check_mint_authority(mint_address),
                'holder_distribution': await self.check_holder_distribution(mint_address),
                'metadata_valid': self.check_metadata_validity(token_info)
            }
            
            # Evaluate overall safety
            safety_result = self.evaluate_safety(safety_checks)
            
            self.logger.info(f"Safety check for {mint_address}: {safety_result}")
            return safety_result
            
        except Exception as e:
            self.logger.error(f"Error checking token safety: {e}")
            return {'safe': False, 'reason': f'Safety check failed: {str(e)}'}
    
    async def get_token_info(self, mint_address: str) -> Optional[Dict]:
        """Get basic token information from Helius"""
        try:
            url = f"{self.base_url}/tokens/metadata"
            params = {
                'api-key': self.api_key,
                'mint-accounts': mint_address
            }
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data[0] if data else None
                else:
                    self.logger.error(f"Failed to get token info: {response.status}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"Error getting token info: {e}")
            return None
    
    async def check_lp_locked(self, mint_address: str) -> Dict:
        """Check if liquidity pool is locked"""
        try:
            # TODO: Implement actual LP lock check via Helius API
            # This is a placeholder implementation
            
            url = f"{self.base_url}/tokens/{mint_address}/liquidity"
            params = {'api-key': self.api_key}
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    is_locked = data.get('locked', False)
                    lock_duration = data.get('lock_duration', 0)
                    
                    return {
                        'passed': is_locked,
                        'details': f"LP locked for {lock_duration} days" if is_locked else "LP not locked"
                    }
                else:
                    return {'passed': False, 'details': 'Could not verify LP lock status'}
                    
        except Exception as e:
            self.logger.error(f"Error checking LP lock: {e}")
            return {'passed': False, 'details': f'LP check failed: {str(e)}'}
    
    async def check_ownership_renounced(self, mint_address: str) -> Dict:
        """Check if token ownership is renounced"""
        try:
            # TODO: Implement actual ownership check via Helius API
            # This is a placeholder implementation
            
            url = f"{self.base_url}/tokens/{mint_address}/authority"
            params = {'api-key': self.api_key}
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    owner = data.get('update_authority')
                    
                    # Check if ownership is renounced (null or burn address)
                    is_renounced = owner is None or owner == "11111111111111111111111111111111"
                    
                    return {
                        'passed': is_renounced,
                        'details': 'Ownership renounced' if is_renounced else f'Owner: {owner}'
                    }
                else:
                    return {'passed': False, 'details': 'Could not verify ownership status'}
                    
        except Exception as e:
            self.logger.error(f"Error checking ownership: {e}")
            return {'passed': False, 'details': f'Ownership check failed: {str(e)}'}
    
    async def check_mint_authority(self, mint_address: str) -> Dict:
        """Check if mint authority is burned"""
        try:
            # TODO: Implement actual mint authority check via Helius API
            # This is a placeholder implementation
            
            url = f"{self.base_url}/tokens/{mint_address}/mint-authority"
            params = {'api-key': self.api_key}
            
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    mint_authority = data.get('mint_authority')
                    
                    # Check if mint authority is burned
                    is_burned = mint_authority is None or mint_authority == "11111111111111111111111111111111"
                    
                    return {
                        'passed': is_burned,
                        'details': 'Mint authority burned' if is_burned else f'Mint authority: {mint_authority}'
                    }
                else:
                    return {'passed': False, 'details': 'Could not verify mint authority'}
                    
        except Exception as e:
            self.logger.error(f"Error checking mint authority: {e}")
            return {'passed': False, 'details': f'Mint authority check failed: {str(e)}'}
    
    async def check_holder_distribution(self, mint_address: str) -> Dict:
        """Check holder distribution for concentration risk"""
        try:
            # TODO: Implement holder distribution check
            # This is a placeholder implementation
            
            return {
                'passed': True,
                'details': 'Holder distribution acceptable'
            }
            
        except Exception as e:
            self.logger.error(f"Error checking holder distribution: {e}")
            return {'passed': False, 'details': f'Holder distribution check failed: {str(e)}'}
    
    def check_metadata_validity(self, token_info: Dict) -> Dict:
        """Check if token metadata is valid and complete"""
        try:
            required_fields = ['name', 'symbol']
            missing_fields = [field for field in required_fields if not token_info.get(field)]
            
            if missing_fields:
                return {
                    'passed': False,
                    'details': f'Missing metadata fields: {missing_fields}'
                }
            
            # Check for suspicious patterns
            name = token_info.get('name', '').lower()
            symbol = token_info.get('symbol', '').lower()
            
            suspicious_keywords = ['test', 'fake', 'scam', 'rug']
            if any(keyword in name or keyword in symbol for keyword in suspicious_keywords):
                return {
                    'passed': False,
                    'details': 'Suspicious token name or symbol'
                }
            
            return {
                'passed': True,
                'details': 'Metadata valid'
            }
            
        except Exception as e:
            return {'passed': False, 'details': f'Metadata check failed: {str(e)}'}
    
    def evaluate_safety(self, checks: Dict) -> Dict:
        """Evaluate overall safety based on individual checks"""
        config = self.config['safety']
        
        # Required checks based on config
        required_checks = []
        if config['require_lp_locked']:
            required_checks.append('lp_locked')
        if config['require_ownership_renounced']:
            required_checks.append('ownership_renounced')
        if config['require_mint_authority_burned']:
            required_checks.append('mint_authority_burned')
        
        # Check if all required checks passed
        failed_checks = []
        for check_name in required_checks:
            if not checks.get(check_name, {}).get('passed', False):
                failed_checks.append(check_name)
        
        # Additional checks
        if not checks.get('holder_distribution', {}).get('passed', True):
            failed_checks.append('holder_distribution')
        
        if not checks.get('metadata_valid', {}).get('passed', True):
            failed_checks.append('metadata_valid')
        
        # Determine overall safety
        is_safe = len(failed_checks) == 0
        
        return {
            'safe': is_safe,
            'reason': 'All safety checks passed' if is_safe else f'Failed checks: {failed_checks}',
            'details': checks,
            'failed_checks': failed_checks
        }
