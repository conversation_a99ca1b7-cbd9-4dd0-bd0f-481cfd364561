"""
Token Filter - Apply filtering criteria to scanned tokens
Filters tokens based on configurable criteria from config.json
"""

import logging
from typing import Dict, List
from datetime import datetime, timedelta

class TokenFilter:
    """Applies filtering criteria to identify high-potential tokens"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.trading_config = config['trading']
        self.filters_config = config['filters']
        self.safety_config = config['safety']
        
    def apply_filters(self, tokens: List[Dict]) -> List[Dict]:
        """Apply all filters to token list and return qualifying tokens"""
        if not tokens:
            return []
        
        self.logger.info(f"Applying filters to {len(tokens)} tokens")
        
        filtered_tokens = []
        filter_stats = {
            'total': len(tokens),
            'bonding_curve': 0,
            'volume': 0,
            'transactions': 0,
            'holders': 0,
            'market_cap': 0,
            'safety': 0,
            'blacklist': 0,
            'passed': 0
        }
        
        for token in tokens:
            try:
                # Apply each filter in sequence
                if not self.check_bonding_curve(token):
                    filter_stats['bonding_curve'] += 1
                    continue
                
                if not self.check_volume_requirements(token):
                    filter_stats['volume'] += 1
                    continue
                
                if not self.check_transaction_requirements(token):
                    filter_stats['transactions'] += 1
                    continue
                
                if not self.check_holder_requirements(token):
                    filter_stats['holders'] += 1
                    continue
                
                if not self.check_market_cap_requirements(token):
                    filter_stats['market_cap'] += 1
                    continue
                
                if not self.check_safety_requirements(token):
                    filter_stats['safety'] += 1
                    continue
                
                if not self.check_blacklist(token):
                    filter_stats['blacklist'] += 1
                    continue
                
                # Token passed all filters
                token['filter_score'] = self.calculate_filter_score(token)
                filtered_tokens.append(token)
                filter_stats['passed'] += 1
                
            except Exception as e:
                self.logger.error(f"Error filtering token {token.get('mint_address', 'unknown')}: {e}")
                continue
        
        self.log_filter_stats(filter_stats)
        
        # Sort by filter score (highest first)
        filtered_tokens.sort(key=lambda x: x.get('filter_score', 0), reverse=True)
        
        return filtered_tokens
    
    def check_bonding_curve(self, token: Dict) -> bool:
        """Check bonding curve percentage requirements"""
        curve_pct = token.get('bonding_curve_percent', 0)
        min_curve = self.trading_config['bonding_curve_threshold']
        max_curve = self.trading_config['max_bonding_curve']
        
        return min_curve <= curve_pct <= max_curve
    
    def check_volume_requirements(self, token: Dict) -> bool:
        """Check 5-minute volume requirements"""
        volume_5m = token.get('volume_5m', 0)
        min_volume = self.trading_config['min_volume_5m']
        
        return volume_5m >= min_volume
    
    def check_transaction_requirements(self, token: Dict) -> bool:
        """Check 5-minute transaction count requirements"""
        txn_count = token.get('txn_count_5m', 0)
        min_txns = self.trading_config['min_txns_5m']
        
        return txn_count >= min_txns
    
    def check_holder_requirements(self, token: Dict) -> bool:
        """Check holder count requirements"""
        holders = token.get('holders', 0)
        min_holders = self.trading_config['min_holders']
        
        return holders >= min_holders
    
    def check_market_cap_requirements(self, token: Dict) -> bool:
        """Check market cap requirements"""
        market_cap = token.get('market_cap', 0)
        min_cap = self.filters_config['min_market_cap']
        max_cap = self.filters_config['max_market_cap']
        
        return min_cap <= market_cap <= max_cap
    
    def check_safety_requirements(self, token: Dict) -> bool:
        """Check basic safety requirements"""
        # Check top holder concentration
        top_holder_pct = token.get('top_holder_percent', 0)
        max_top_holder = self.safety_config['max_top_holder_percent']
        
        if top_holder_pct > max_top_holder:
            return False
        
        # Check dev buy percentage
        dev_buy_pct = token.get('dev_buy_percent', 0)
        max_dev_buy = self.filters_config['max_dev_buy_percent']
        
        if dev_buy_pct > max_dev_buy:
            return False
        
        # Check minimum unique holders
        unique_holders = token.get('holders', 0)
        min_unique = self.safety_config['min_unique_holders']
        
        return unique_holders >= min_unique
    
    def check_blacklist(self, token: Dict) -> bool:
        """Check if token is blacklisted"""
        mint_address = token.get('mint_address', '')
        blacklist = self.filters_config['blacklisted_tokens']
        
        return mint_address not in blacklist
    
    def calculate_filter_score(self, token: Dict) -> float:
        """Calculate a score for ranking filtered tokens"""
        score = 0.0
        
        # Volume score (higher is better)
        volume_5m = token.get('volume_5m', 0)
        score += min(volume_5m / 10000, 10)  # Max 10 points
        
        # Transaction velocity score
        txn_count = token.get('txn_count_5m', 0)
        score += min(txn_count / 100, 5)  # Max 5 points
        
        # Holder growth score
        holders = token.get('holders', 0)
        score += min(holders / 50, 3)  # Max 3 points
        
        # Bonding curve position score (sweet spot around 10-30%)
        curve_pct = token.get('bonding_curve_percent', 0)
        if 10 <= curve_pct <= 30:
            score += 5
        elif 5 <= curve_pct <= 50:
            score += 3
        else:
            score += 1
        
        # Liquidity score
        liquidity = token.get('liquidity', 0)
        score += min(liquidity / 1000, 2)  # Max 2 points
        
        return round(score, 2)
    
    def log_filter_stats(self, stats: Dict):
        """Log filtering statistics"""
        total = stats['total']
        passed = stats['passed']
        
        self.logger.info(f"Filter Results: {passed}/{total} tokens passed ({passed/total*100:.1f}%)")
        
        if self.config['logging']['detailed_logging']:
            self.logger.info(f"Filter breakdown:")
            self.logger.info(f"  - Bonding curve: {stats['bonding_curve']} filtered")
            self.logger.info(f"  - Volume: {stats['volume']} filtered")
            self.logger.info(f"  - Transactions: {stats['transactions']} filtered")
            self.logger.info(f"  - Holders: {stats['holders']} filtered")
            self.logger.info(f"  - Market cap: {stats['market_cap']} filtered")
            self.logger.info(f"  - Safety: {stats['safety']} filtered")
            self.logger.info(f"  - Blacklist: {stats['blacklist']} filtered")
    
    def get_top_tokens(self, filtered_tokens: List[Dict], limit: int = 5) -> List[Dict]:
        """Get top N tokens by filter score"""
        return filtered_tokens[:limit]
