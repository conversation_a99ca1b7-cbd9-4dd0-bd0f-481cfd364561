"""
Trade Logger - Comprehensive logging and metrics tracking
Logs all trades with detailed metrics and stores in SQLite database
"""

import sqlite3
import logging
import json
import csv
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

class TradeLogger:
    """Handles trade logging and metrics storage"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.db_path = config['logging']['database_file']
        
        # Initialize database
        self.init_database()
    
    def init_database(self):
        """Initialize SQLite database with required tables"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create trades table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trades (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trade_id TEXT UNIQUE NOT NULL,
                    mint_address TEXT NOT NULL,
                    token_name TEXT,
                    token_symbol TEXT,
                    wallet_address TEXT,
                    
                    -- Entry data
                    entry_timestamp TEXT NOT NULL,
                    entry_price REAL NOT NULL,
                    entry_amount_sol REAL NOT NULL,
                    token_amount REAL NOT NULL,
                    entry_tx_id TEXT,
                    entry_gas_fee REAL,
                    
                    -- Exit data
                    exit_timestamp TEXT,
                    exit_price REAL,
                    exit_amount_sol REAL,
                    exit_tx_id TEXT,
                    exit_gas_fee REAL,
                    exit_reason TEXT,
                    
                    -- Performance metrics
                    hold_time_seconds INTEGER,
                    pnl_sol REAL,
                    pnl_percentage REAL,
                    is_winner BOOLEAN,
                    
                    -- Token metrics at entry
                    bonding_curve_percent REAL,
                    volume_5m REAL,
                    txn_count_5m INTEGER,
                    holders INTEGER,
                    market_cap REAL,
                    
                    -- Additional data
                    slippage_used REAL,
                    filter_score REAL,
                    safety_checks TEXT,
                    notes TEXT,
                    
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create performance summary table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_summary (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    total_trades INTEGER DEFAULT 0,
                    winning_trades INTEGER DEFAULT 0,
                    losing_trades INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0,
                    total_pnl_sol REAL DEFAULT 0,
                    avg_pnl_percentage REAL DEFAULT 0,
                    best_trade_pnl REAL DEFAULT 0,
                    worst_trade_pnl REAL DEFAULT 0,
                    avg_hold_time_minutes REAL DEFAULT 0,
                    total_volume_sol REAL DEFAULT 0,
                    total_gas_fees REAL DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date)
                )
            ''')
            
            # Create indexes for better query performance
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(entry_timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_mint ON trades(mint_address)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_trades_wallet ON trades(wallet_address)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_date ON performance_summary(date)')
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Database initialized: {self.db_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise
    
    def log_trade_entry(self, trade_data: Dict) -> str:
        """Log a new trade entry and return trade_id"""
        try:
            trade_id = f"trade_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{trade_data['mint_address'][:8]}"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO trades (
                    trade_id, mint_address, token_name, token_symbol, wallet_address,
                    entry_timestamp, entry_price, entry_amount_sol, token_amount,
                    entry_tx_id, entry_gas_fee, bonding_curve_percent, volume_5m,
                    txn_count_5m, holders, market_cap, slippage_used, filter_score,
                    safety_checks
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                trade_id,
                trade_data['mint_address'],
                trade_data.get('token_name', ''),
                trade_data.get('token_symbol', ''),
                trade_data.get('wallet_address', ''),
                trade_data['entry_timestamp'],
                trade_data['entry_price'],
                trade_data['entry_amount_sol'],
                trade_data['token_amount'],
                trade_data.get('entry_tx_id', ''),
                trade_data.get('entry_gas_fee', 0),
                trade_data.get('bonding_curve_percent', 0),
                trade_data.get('volume_5m', 0),
                trade_data.get('txn_count_5m', 0),
                trade_data.get('holders', 0),
                trade_data.get('market_cap', 0),
                trade_data.get('slippage_used', 0),
                trade_data.get('filter_score', 0),
                json.dumps(trade_data.get('safety_checks', {}))
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Logged trade entry: {trade_id}")
            return trade_id
            
        except Exception as e:
            self.logger.error(f"Failed to log trade entry: {e}")
            return ""
    
    def log_trade_exit(self, trade_id: str, exit_data: Dict):
        """Log trade exit and calculate performance metrics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get entry data
            cursor.execute('SELECT entry_timestamp, entry_price, entry_amount_sol FROM trades WHERE trade_id = ?', (trade_id,))
            entry_row = cursor.fetchone()
            
            if not entry_row:
                self.logger.error(f"Trade {trade_id} not found for exit logging")
                return
            
            entry_timestamp, entry_price, entry_amount_sol = entry_row
            
            # Calculate performance metrics
            entry_time = datetime.fromisoformat(entry_timestamp)
            exit_time = datetime.fromisoformat(exit_data['exit_timestamp'])
            hold_time_seconds = int((exit_time - entry_time).total_seconds())
            
            exit_amount_sol = exit_data.get('exit_amount_sol', 0)
            pnl_sol = exit_amount_sol - entry_amount_sol - exit_data.get('exit_gas_fee', 0)
            pnl_percentage = (pnl_sol / entry_amount_sol) * 100 if entry_amount_sol > 0 else 0
            is_winner = pnl_sol > 0
            
            # Update trade record
            cursor.execute('''
                UPDATE trades SET
                    exit_timestamp = ?, exit_price = ?, exit_amount_sol = ?,
                    exit_tx_id = ?, exit_gas_fee = ?, exit_reason = ?,
                    hold_time_seconds = ?, pnl_sol = ?, pnl_percentage = ?, is_winner = ?
                WHERE trade_id = ?
            ''', (
                exit_data['exit_timestamp'],
                exit_data.get('exit_price', 0),
                exit_amount_sol,
                exit_data.get('exit_tx_id', ''),
                exit_data.get('exit_gas_fee', 0),
                exit_data.get('exit_reason', ''),
                hold_time_seconds,
                pnl_sol,
                pnl_percentage,
                is_winner,
                trade_id
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Logged trade exit: {trade_id} - PnL: {pnl_sol:.4f} SOL ({pnl_percentage:.2f}%)")
            
            # Update daily performance summary
            self.update_daily_summary(exit_time.date())
            
        except Exception as e:
            self.logger.error(f"Failed to log trade exit: {e}")
    
    def update_daily_summary(self, date):
        """Update daily performance summary"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            date_str = date.strftime('%Y-%m-%d')
            
            # Calculate daily metrics
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN is_winner = 1 THEN 1 ELSE 0 END) as winning_trades,
                    SUM(CASE WHEN is_winner = 0 THEN 1 ELSE 0 END) as losing_trades,
                    AVG(CASE WHEN is_winner = 1 THEN 1.0 ELSE 0.0 END) * 100 as win_rate,
                    SUM(pnl_sol) as total_pnl_sol,
                    AVG(pnl_percentage) as avg_pnl_percentage,
                    MAX(pnl_sol) as best_trade_pnl,
                    MIN(pnl_sol) as worst_trade_pnl,
                    AVG(hold_time_seconds / 60.0) as avg_hold_time_minutes,
                    SUM(entry_amount_sol) as total_volume_sol,
                    SUM(COALESCE(entry_gas_fee, 0) + COALESCE(exit_gas_fee, 0)) as total_gas_fees
                FROM trades 
                WHERE DATE(entry_timestamp) = ? AND exit_timestamp IS NOT NULL
            ''', (date_str,))
            
            metrics = cursor.fetchone()
            
            if metrics and metrics[0] > 0:  # If there are completed trades
                # Insert or update daily summary
                cursor.execute('''
                    INSERT OR REPLACE INTO performance_summary (
                        date, total_trades, winning_trades, losing_trades, win_rate,
                        total_pnl_sol, avg_pnl_percentage, best_trade_pnl, worst_trade_pnl,
                        avg_hold_time_minutes, total_volume_sol, total_gas_fees
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (date_str, *metrics))
                
                conn.commit()
            
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Failed to update daily summary: {e}")
    
    def get_trade_history(self, limit: int = 100) -> List[Dict]:
        """Get recent trade history"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM trades 
                ORDER BY entry_timestamp DESC 
                LIMIT ?
            ''', (limit,))
            
            columns = [description[0] for description in cursor.description]
            trades = []
            
            for row in cursor.fetchall():
                trade = dict(zip(columns, row))
                # Parse JSON fields
                if trade['safety_checks']:
                    trade['safety_checks'] = json.loads(trade['safety_checks'])
                trades.append(trade)
            
            conn.close()
            return trades
            
        except Exception as e:
            self.logger.error(f"Failed to get trade history: {e}")
            return []
    
    def get_performance_stats(self, days: int = 30) -> Dict:
        """Get performance statistics for the last N days"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_trades,
                    SUM(CASE WHEN is_winner = 1 THEN 1 ELSE 0 END) as winning_trades,
                    AVG(CASE WHEN is_winner = 1 THEN 1.0 ELSE 0.0 END) * 100 as win_rate,
                    SUM(pnl_sol) as total_pnl_sol,
                    AVG(pnl_percentage) as avg_pnl_percentage,
                    MAX(pnl_percentage) as best_trade_pct,
                    MIN(pnl_percentage) as worst_trade_pct,
                    AVG(hold_time_seconds / 60.0) as avg_hold_time_minutes,
                    SUM(entry_amount_sol) as total_volume_sol
                FROM trades 
                WHERE DATE(entry_timestamp) >= DATE('now', '-{} days') 
                AND exit_timestamp IS NOT NULL
            '''.format(days))
            
            row = cursor.fetchone()
            conn.close()
            
            if row and row[0] > 0:
                return {
                    'total_trades': row[0],
                    'winning_trades': row[1],
                    'losing_trades': row[0] - row[1],
                    'win_rate': round(row[2] or 0, 2),
                    'total_pnl_sol': round(row[3] or 0, 4),
                    'avg_pnl_percentage': round(row[4] or 0, 2),
                    'best_trade_pct': round(row[5] or 0, 2),
                    'worst_trade_pct': round(row[6] or 0, 2),
                    'avg_hold_time_minutes': round(row[7] or 0, 2),
                    'total_volume_sol': round(row[8] or 0, 4),
                    'days': days
                }
            else:
                return {'total_trades': 0, 'days': days}
                
        except Exception as e:
            self.logger.error(f"Failed to get performance stats: {e}")
            return {'error': str(e)}
    
    def export_to_csv(self, filename: str = None) -> str:
        """Export trade data to CSV file"""
        try:
            if not filename:
                filename = f"trades_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            trades = self.get_trade_history(limit=10000)  # Get all trades
            
            if not trades:
                return "No trades to export"
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = trades[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for trade in trades:
                    # Convert complex fields to strings
                    row = trade.copy()
                    if isinstance(row.get('safety_checks'), dict):
                        row['safety_checks'] = json.dumps(row['safety_checks'])
                    writer.writerow(row)
            
            self.logger.info(f"Exported {len(trades)} trades to {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Failed to export to CSV: {e}")
            return f"Export failed: {e}"
